import { Card, CardContent,  CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ChevronRight } from "lucide-react"
import { Link } from "react-router-dom"

interface MetricCardProps {
  title: string
  metric: string | number
  viewAllLink?: string
}

export function MetricCard({ title, metric, viewAllLink }: MetricCardProps) {
  return (
    <Card className="p-6">
      <CardContent className="p-0">
        <CardTitle className="text-lg font-medium text-gray-900 mb-6">
          {title}
        </CardTitle>
        <Separator className="mb-4" />
        <div className="text-4xl font-bold text-gray-900 mb-4">
          {metric}
        </div>
        {viewAllLink && (
          <Link 
            to={viewAllLink}
            className="text-[#FB923C] hover:text-orange-300 text-[14px] font-medium inline-flex items-center gap-1"
          >
            View all
            <ChevronRight className="ml-1 w-[16px] h-[16px]" />
          </Link>
        )}
      </CardContent>
    </Card>
  )
}
import { Card, CardContent,  CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

interface Project {
  id: string
  title: string
  fundingSought: number
  investmentType: 'Equity' | 'Acquisition' | 'Debt'
}

interface ProjectListProps {
  title: string
  projects: Project[]
}

const formatCurrency = (amount: number) => {
  if (amount >= 1000) {
    return `$ ${amount / 1000}K`
  }
  return `$ ${amount}`
}

const getInvestmentTypeColor = (type: Project['investmentType']) => {
  switch (type) {
    case 'Equity':
      return 'text-[#FB923C]'
    case 'Acquisition':
      return 'text-[#FB923C]'
    case 'Debt':
      return 'text-blue-500'
    default:
      return 'text-gray-500'
  }
}

export function ProjectList({ title, projects }: ProjectListProps) {
  const shouldScroll = projects.length > 3

  return (
    <Card className="p-6">
      <CardContent className="p-0">
        <CardTitle className="text-lg font-medium text-black mb-6">
          {title}
        </CardTitle>

        <Separator className="mb-4" />

        <div 
          className={`space-y-6 ${shouldScroll ? 'max-h-80 overflow-y-auto pr-2 custom-scrollbar' : ''}`}
        >
          {projects.map((project) => (
            <div key={project.id} className="space-y-2 rounded-lg border border-gray-200 p-4 hover:bg-gray-50 transition-colors">
              <h3 className="text-sm font-medium text-black leading-5">
                {project.title}
              </h3>
              
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-sm text-black">
                  <span>Funding Sought:</span>
                  <span className="font-medium">{formatCurrency(project.fundingSought)}</span>
                </div>
                
                <div className="flex items-center gap-2 text-sm text-black">
                  <span>Investment type:</span>
                  <span className={`font-medium ${getInvestmentTypeColor(project.investmentType)}`}>
                    {project.investmentType}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
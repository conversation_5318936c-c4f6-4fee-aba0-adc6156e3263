import React from 'react';
import Layout from './layout'; 
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from '@/pages/home';
import Users from '@/pages/users/index';
import Profile from '@/pages/users/profile';
import ProjectManagement from '@/pages/project management/project-management';
import ViewProject from '@/pages/project management/view-project';
import FundManagement from '@/pages/fund management/fund-management';
import Contracts from '@/pages/contracts';
import ViewContract from '@/pages/contracts/view-contract';
import Compliance from '@/pages/compliance';
import ComplianceProfileView from '@/pages/compliance/profile';
import Reports from '@/pages/report/reports';
import Settings from '@/pages/settings/settings';

const Root: React.FC = () => {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="users">
            <Route index element={<Users />} />
            <Route path="profile/:id" element={<Profile />} />
          </Route>
          <Route path="/project-management" element={<ProjectManagement />} />
          <Route path="/project-management/view-project/:id" element={<ViewProject />} />
          <Route path="/fund-management" element={<FundManagement />} />

          <Route path="contracts">
            <Route index element={<Contracts />} />
            <Route path=":id" element={<ViewContract />} />
          </Route>

          <Route path="compliance">
            <Route index element={<Compliance />} />
            <Route path="profile/:id" element={<ComplianceProfileView />} />
          </Route>

          <Route path="/reports" element={<Reports />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Layout>
    </Router>
  );
};

export default Root;

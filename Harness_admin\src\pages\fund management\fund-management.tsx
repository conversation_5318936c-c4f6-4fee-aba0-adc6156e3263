import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useNavigate } from 'react-router-dom';

interface FundProject {
  id: number;
  name: string;
  capacity: string;
  business: {
    name: string;
    shortName: string;
    color: string;
  };
  investors: string;
  investorCount?: number;
  totalDisbursed: string;
  pendingDisbursement: string;
  latestPayment: string;
}

const FundManagement: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const navigate = useNavigate();
  const itemsPerPage = 10;
  
  const fundProjects: FundProject[] = [
    {
      id: 1,
      name: 'SunPower Ghana',
      capacity: '50MW',
      business: { name: 'GreenSun Energy Ltd.', shortName: 'GS', color: 'bg-purple-500' },
      investors: 'FutureEnergy Ventures',
      investorCount: 1,
      totalDisbursed: '$ 180,000',
      pendingDisbursement: '$ 180,000',
      latestPayment: '10%'
    },
    {
      id: 2,
      name: 'Nairobi Rooftop Grid',
      capacity: '5MW',
      business: { name: 'AfroSolar Co.', shortName: 'A', color: 'bg-amber-500' },
      investors: 'Impact Capital Africa',
      totalDisbursed: '$ 120,000',
      pendingDisbursement: '$ 120,000',
      latestPayment: '5%'
    },
    {
      id: 3,
      name: 'SolarEV Nigeria',
      capacity: '12MW',
      business: { name: 'GreenFuture Tech', shortName: 'GF', color: 'bg-green-500' },
      investors: 'Afrinvest Green Fund',
      investorCount: 3,
      totalDisbursed: '$ 200,000',
      pendingDisbursement: '$ 200,000',
      latestPayment: '12%'
    },
    {
      id: 4,
      name: 'Kisumu Solar Farm',
      capacity: '20MW',
      business: { name: 'BrightFuture Kenya Ltd.', shortName: 'BF', color: 'bg-blue-500' },
      investors: 'CleanFund',
      investorCount: 5,
      totalDisbursed: '$ 200,000',
      pendingDisbursement: '$ 200,000',
      latestPayment: '12%'
    },
    {
      id: 5,
      name: 'Ashanti Microgrid Project',
      capacity: '3MW',
      business: { name: 'EcoEnergy Ghana Ltd.', shortName: 'E', color: 'bg-blue-700' },
      investors: 'Renew Impact Partners',
      totalDisbursed: '$ 200,000',
      pendingDisbursement: '$ 200,000',
      latestPayment: '12%'
    },
    {
      id: 6,
      name: 'Enugu Smart Solar',
      capacity: '15MW',
      business: { name: 'RenewNaija', shortName: 'RN', color: 'bg-cyan-500' },
      investors: 'Sunrise Equity Partners',
      totalDisbursed: '$ 200,000',
      pendingDisbursement: '$ 200,000',
      latestPayment: '12%'
    },
    {
      id: 7,
      name: 'Limpopo Solar Park',
      capacity: '25MW',
      business: { name: 'PanelSA', shortName: 'P', color: 'bg-pink-500' },
      investors: 'Afronvest Group',
      investorCount: 5,
      totalDisbursed: '$ 200,000',
      pendingDisbursement: '$ 200,000',
      latestPayment: '12%'
    },
    {
      id: 8,
      name: 'Kumasi Commercial Rooftops',
      capacity: '10MW',
      business: { name: 'SolarInvest Africa', shortName: 'S', color: 'bg-green-500' },
      investors: 'East Africa Capital',
      totalDisbursed: '$ 200,000',
      pendingDisbursement: '$ 200,000',
      latestPayment: '12%'
    },
    {
      id: 9,
      name: 'Banjul Urban Solar',
      capacity: '5.5MW',
      business: { name: 'Green Gambia Tech', shortName: 'GG', color: 'bg-cyan-500' },
      investors: 'GreenScale Ventures',
      totalDisbursed: '$ 200,000',
      pendingDisbursement: '$ 200,000',
      latestPayment: '12%'
    },
    {
      id: 10,
      name: 'Central Ghana MiniGrid',
      capacity: '7MW',
      business: { name: 'Sunrise Infrastructure', shortName: 'S', color: 'bg-purple-500' },
      investors: 'ImpactCircle',
      totalDisbursed: '$ 200,000',
      pendingDisbursement: '$ 200,000',
      latestPayment: '12%'
    }
  ];

  const filteredProjects = fundProjects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.business.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.investors.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalPages = Math.ceil(filteredProjects.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, filteredProjects.length);
  const currentData = filteredProjects.slice(startIndex, endIndex);

  const handleProjectClick = (projectId: number) => {
    navigate(`/fund-management/view-funding/${projectId}`);
  };

  return (
    <div className="w-full h-full px-6 py-4 overflow-hidden flex flex-col">
      <div className="flex flex-col space-y-6 h-full">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Fund Management</h1>
        </div>
        
        <div className="relative max-w-md">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="search"
            placeholder="Search"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden flex-1 flex flex-col">
          <ScrollArea className="flex-1">
            <div className="overflow-x-auto min-w-full">
              <table className="w-full min-w-[800px]">
                <thead className="sticky top-0 bg-gray-50 z-10">
                  <tr className="border-b border-gray-200 text-left">
                    <th className="px-4 py-3 text-sm font-medium text-gray-500">Project name</th>
                    <th className="px-4 py-3 text-sm font-medium text-gray-500">Business</th>
                    <th className="px-4 py-3 text-sm font-medium text-gray-500">Investors</th>
                    <th className="px-4 py-3 text-sm font-medium text-gray-500">Total Disbursed</th>
                    <th className="px-4 py-3 text-sm font-medium text-gray-500">Pending Disbursement</th>
                    <th className="px-4 py-3 text-sm font-medium text-gray-500">Latest Payment</th>
                  </tr>
                </thead>
                <tbody>
                  {currentData.map((project) => (
                    <tr 
                      key={project.id} 
                      className="border-b border-gray-200 hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleProjectClick(project.id)}
                    >
                      <td className="px-4 py-4">
                        <div className="font-medium">{project.name}</div>
                        <div className="text-sm text-gray-500">({project.capacity})</div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex items-center gap-2">
                          <div className={`w-8 h-8 rounded-full ${project.business.color} text-white flex items-center justify-center text-sm font-medium`}>
                            {project.business.shortName}
                          </div>
                          <span>{project.business.name}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex items-center gap-1">
                          <span>{project.investors}</span>
                          {project.investorCount && (
                            <span className="bg-gray-100 text-gray-700 text-xs px-1 rounded-md">+{project.investorCount}</span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4">{project.totalDisbursed}</td>
                      <td className="px-4 py-4">{project.pendingDisbursement}</td>
                      <td className="px-4 py-4">{project.latestPayment}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </ScrollArea>
          
          {/* Pagination */}
          <div className="flex items-center justify-start space-x-6 py-4">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="text-gray-500 disabled:opacity-50"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNumber = i + 1;
              return (
                <button
                  key={pageNumber}
                  onClick={() => setCurrentPage(pageNumber)}
                  className={`h-8 w-8 flex items-center justify-center rounded-full text-sm font-medium ${
                    currentPage === pageNumber
                      ? 'bg-amber-100 text-black'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {pageNumber}
                </button>
              );
            })}

            {totalPages > 5 && <span className="text-gray-500">...</span>}

            {totalPages > 5 && (
              <button
                onClick={() => setCurrentPage(totalPages)}
                className="h-8 w-8 flex items-center justify-center rounded-full text-sm font-medium text-gray-700 hover:bg-gray-100"
              >
                {totalPages}
              </button>
            )}

            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="text-gray-500 disabled:opacity-50"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FundManagement;



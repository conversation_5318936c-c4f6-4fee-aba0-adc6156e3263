import { Card, CardContent,  CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";
import {Separator } from "@/components/ui/separator";
import { ChevronRight } from "lucide-react";

interface Investment {
  id: string
  title: string
  currentAmount: number
  targetAmount: number
  status: 'on-track' | 'warning' | 'critical'
}

interface OngoingInvestmentsProps {
  investments: Investment[]
  viewAllLink?: string
}

const getStatusColor = (status: Investment['status']) => {
  switch (status) {
    case 'on-track':
      return 'bg-green-500'
    case 'warning':
      return 'bg-yellow-500'
    case 'critical':
      return 'bg-red-500'
    default:
      return 'bg-gray-300'
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0, 
  }).format(amount)
}

export function OngoingInvestments({ investments, viewAllLink }: OngoingInvestmentsProps) {
  return (
    <Card className="p-6">
      <CardContent className="p-0">
        <div className="flex items-center justify-between mb-6">
          <CardTitle className="text-lg font-medium text-gray-900">
            Ongoing Investments
          </CardTitle>
          {viewAllLink && (
            <Link 
              to={viewAllLink}
              className="text-[#FB923C] hover:text-orange-300 text-lg font-medium inline-flex items-center gap-1"
            >
              View all projects
              <ChevronRight className="ml-1 w-[16px] h-[16px] rotate-90" />
            </Link>
          )}
        </div>
        <Separator className="mb-4" />
        
        <div className="space-y-6">
          {investments.slice(0, 4).map((investment) => {
            const progressPercentage = (investment.currentAmount / investment.targetAmount) * 100
            
            return (
              <div key={investment.id} className="space-y-3">
                <h3 className="text-sm font-medium text-black leading-5">
                  {investment.title}
                </h3>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-xs text-black">
                    <span>{formatCurrency(investment.currentAmount)}</span>
                    <span>{formatCurrency(investment.targetAmount)}</span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${getStatusColor(investment.status)}`}
                      style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
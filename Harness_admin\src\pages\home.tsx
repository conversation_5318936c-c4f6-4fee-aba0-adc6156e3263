import { MetricCard } from '@/components/home/<USER>'
import { OngoingInvestments } from '@/components/home/<USER>'
import { ProjectList } from '@/components/home/<USER>'

const metricCards = [
  { title: 'Total Projects Submitted', metric: 71, viewAllLink: '/projects' },
  { title: 'Active Developers', metric: 50, viewAllLink: '/developers' },
  { title: 'Active Investors', metric: 120, viewAllLink: '/investors' },
  { title: 'Total Funds Committed', metric: '$ 100000', viewAllLink: '/funds' },
  { title: 'Contracts Signed', metric: 53, viewAllLink: '/contracts' },
  { title: 'Contracts Pending', metric: 3, viewAllLink: '/contracts/pending' },
]

const ongoingInvestments = [
  {
    id: '1',
    title: '8MW BESS + 2.5MWp PV hybrid in Accra',
    currentAmount: 100000,
    targetAmount: 150000,
    status: 'on-track' as const,
  },
  {
    id: '2',
    title: '100MWp late-stage solar project in Nairobi',
    currentAmount: 49000,
    targetAmount: 60000,
    status: 'on-track' as const,
  },
  {
    id: '3',
    title: 'Early-stage 72 MW BESS in Accra',
    currentAmount: 50000,
    targetAmount: 80000,
    status: 'warning' as const,
  },
  {
    id: '4',
    title: '10MW PV Ready to Build - Ghana',
    currentAmount: 25000,
    targetAmount: 120000,
    status: 'critical' as const,
  },
]

const topPerformingProjects = [
  {
    id: '1',
    title: '7MWp PV + 6MWh BESS Ready to...',
    fundingSought: 200,
    investmentType: 'Equity' as const,
  },
  {
    id: '2',
    title: '100 MW wind farm hybrid in South...',
    fundingSought: 200,
    investmentType: 'Acquisition' as const,
  },
  {
    id: '3',
    title: '10 MW Early Stage Distributed Sol...',
    fundingSought: 200,
    investmentType: 'Equity' as const,
  },
    {
        id: '4',
        title: '5 MW Solar PV + 2 MWh BESS in...',
        fundingSought: 150,
        investmentType: 'Debt' as const,
    },
    {
        id: '5',
        title: '20 MW Wind Farm in Coastal Region',
        fundingSought: 300,
        investmentType: 'Equity' as const,
    },
]

const projectsNeedingAttention = [
  {
    id: '1',
    title: '7MWp PV + 6MWh BESS Ready to...',
    fundingSought: 200,
    investmentType: 'Equity' as const,
  },
  {
    id: '2',
    title: '100 MW wind farm hybrid in South...',
    fundingSought: 200,
    investmentType: 'Acquisition' as const,
  },
  {
    id: '3',
    title: '10 MW Early Stage Distributed Sol...',
    fundingSought: 200,
    investmentType: 'Equity' as const,
  },
    {
        id: '4',
        title: '5 MW Solar PV + 2 MWh BESS in...',
        fundingSought: 150,
        investmentType: 'Debt' as const,
    },
    {
        id: '5',
        title: '20 MW Wind Farm in Coastal Region',
        fundingSought: 300,
        investmentType: 'Equity' as const,
    },
]

export default function Dashboard() {
  return (
    <div className="min-h-full bg-white p-6">
      <div className="space-y-6">
        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {metricCards.map((card, index) => (
            <MetricCard
              key={index}
              title={card.title}
              metric={card.metric}
              viewAllLink={card.viewAllLink}
            />
          ))}
        </div>
        
        {/* Bottom Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Ongoing Investments */}
          <div className="lg:col-span-1">
            <OngoingInvestments
              investments={ongoingInvestments}
              viewAllLink="/investments"
            />
          </div>
          
          {/* Top Performing Projects */}
          <div className="lg:col-span-1">
            <ProjectList
              title="Top Performing Projects"
              projects={topPerformingProjects}
            />
          </div>
          
          {/* Projects Needing Attention */}
          <div className="lg:col-span-1">
            <ProjectList
              title="Projects Needing Attention"
              projects={projectsNeedingAttention}
            />
          </div>
        </div>
      </div>
    </div>
  )
}